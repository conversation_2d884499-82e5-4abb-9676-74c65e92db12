import { useEffect } from 'react';
import { Modal, ModalContent, ModalBody, useDisclosure } from '@heroui/react';
import { SortIcon } from '@/components/icons';
import RecommendationCard from './card';
import { useSession } from 'next-auth/react';
import Cookies from 'js-cookie';
import { useDispatch, useSelector } from 'react-redux';
import {
  setRecommendations,
  toggleType,
  setCity,
  applyFilters,
  resetFilters,
  selectFilteredRecommendations,
  selectFilterTypes,
  selectSelectedTypes,
  selectCities,
  selectSelectedCity,
} from '@/slices/recommendationSlice';
import { useGetRecommendationsMutation } from '@/services/recommendationApi';
import RecommendationFilter from '@/components/globalComponents/Filter/RecommendationFilter';

const Recommendation = () => {
  const { data: session } = useSession() ?? { data: null };
  const dispatch = useDispatch();

  // 🔥 Use RTK Query mutation hook
  const [getRecommendations, { isLoading }] = useGetRecommendationsMutation();

  // 🔥 Use slice selectors
  const recommendations = useSelector(selectFilteredRecommendations);
  const filterTypes = useSelector(selectFilterTypes);
  const selectedTypes = useSelector(selectSelectedTypes);
  const citesList = useSelector(selectCities);
  const selectedCity = useSelector(selectSelectedCity);

  const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();

  // Fetch data on mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getRecommendations({
          page: 1,
          pageSize: 10,
        }).unwrap();

        if (res.detail.data.recommendations) {
          dispatch(setRecommendations(res.detail.data.recommendations));
        }
      } catch (err) {
        dispatch(setRecommendations([]));
      }
    };

    fetchData();
  }, [dispatch, getRecommendations, Cookies.get('guest_id'), session?.user]);

  useEffect(() => {
    if (!isOpen) {
      onClose();
    }
  }, [onOpen, onClose]);

  const handleChange = (type: string) => {
    dispatch(toggleType(type));
  };

  // Change selected city
  const handleCitesChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    dispatch(setCity(event.target.value || null));
  };

  // Apply filters from slice
  const filterData = () => {
    dispatch(applyFilters());
    onClose();
  };

  // Reset all filters
  const resetFilterData = () => {
    dispatch(resetFilters());
  };

  return (
    <div className="relative">
      {/* Header */}
      <div className="flex flex-row items-center justify-between flex-shrink-0 md:sticky md:top-0 md:z-50 mb-2 bg-[#F2F2FF]">
        <div>
          <p className="text-lg font-bold">Recommendation</p>
        </div>
        <div className="flex flex-row items-center gap-2 cursor-pointer">
          <SortIcon isAnimation={false} className="text-default-Secondary" />
          <span
            role="button"
            tabIndex={0}
            onClick={onOpen}
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                onOpen();
              }
            }}
            className="text-sm font-medium text-default-Secondary cursor-pointer"
          >
            Filter
          </span>
        </div>
      </div>

      {/* Loading state */}
      {isLoading && <p>Loading recommendations...</p>}

      {/* List */}
      {recommendations?.map(item => (
        <RecommendationCard key={item.title} recommendations={item} />
      ))}

      {/* this is the RecommendationFilter use this insted of modal */}
      <RecommendationFilter
        open={isOpen}
        onClose={onClose}
        onApply={filterData}
      />

      {/* Filter Modal */}
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} size="xl">
        <ModalContent>
          <ModalBody className="p-6">
            <p className="text-lg font-bold">Recommendation Filter</p>

            {/* Type Filters */}
            <div>
              <p className="text-default-700">Sort By</p>
              {filterTypes.map(type => (
                <label key={type} style={{ display: 'block' }}>
                  <input
                    type="checkbox"
                    className="w-[20px] pe-3"
                    value={type}
                    checked={selectedTypes.includes(type)}
                    onChange={() => handleChange(type)}
                  />
                  {type}
                </label>
              ))}
            </div>

            {/* City Filter */}
            <div>
              <p className="text-default-700">Location</p>
              <div className="flex flex-col gap-2">
                <select
                  value={selectedCity || ''}
                  onChange={handleCitesChange}
                  className="border p-2 rounded"
                >
                  <option value="">Any Area...</option>
                  {citesList.map(city => (
                    <option key={city} value={city}>
                      {city}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Footer Actions */}
            <div className="w-full flex items-center justify-between gap-4 ">
              <button
                type="button"
                className="font-medium cursor-pointer text-primary-200"
                onClick={resetFilterData}
              >
                Reset All
              </button>
              <button
                type="button"
                className="font-semibold rounded-full bg-primary-200 text-white px-4 py-2"
                onClick={filterData}
              >
                Apply
              </button>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default Recommendation;

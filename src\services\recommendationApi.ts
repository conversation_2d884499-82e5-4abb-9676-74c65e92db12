// src/services/recommendationApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import Cookies from 'js-cookie';

interface Recommendation {
  title: string;
  duration: string;
  location: string;
  tags: string;
  image: string;
  imagesm1: string;
  imagesm2: string;
  imagesm3: string;
  imagesm4: string;
  badge: string;
  type: string | string[];
  city: string;
  country: string;
  image_url: string;
  theme: string[];
  description: string;
  rating: number;
  review_count: number;
  sample_experiences: [];
}

export const recommendationApi = createApi({
  reducerPath: 'recommendationApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT,
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as any).auth?.token;
      console.log('token', token);
      if (token) {
        console.log('hi');
        headers.set('authorization', `Bearer ${token}`);
      } else {
        const guestId = localStorage.getItem('guest_id');
        if (guestId) {
          headers.set('x-guest-id', guestId);
        }
      }
      return headers;
    },
  }),
  tagTypes: ['Recommendations'],
  endpoints: builder => ({
    getRecommendations: builder.mutation<
      { detail: { data: { recommendations: Recommendation[] } } },
      { page?: number; pageSize?: number }
    >({
      query: ({ page = 1, pageSize = 10 }) => ({
        url: `/api/v1/search/recommendation?display_type=recommendations&page=${page}&page_size=${pageSize}`,
        method: 'POST',
        body: {}, // send empty body if not needed
      }),
      invalidatesTags: ['Recommendations'],
    }),
  }),
});

export const { useGetRecommendationsMutation } = recommendationApi;
export type { Recommendation };

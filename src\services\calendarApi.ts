// src/services/calendarApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

interface CalendarRequest {
  startDate: string;
  endDate: string;
}

export const calendarApi = createApi({
  reducerPath: 'calendarApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT,
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as any).auth?.token;

      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      } else {
        const guestId = localStorage.getItem('guest_id');
        if (guestId) {
          headers.set('x-guest-id', guestId);
        }
      }
      return headers;
    },
  }),
  tagTypes: ['CalendarDatesList'],
  endpoints: builder => ({
    getDatesList: builder.mutation<any, CalendarRequest>({
      query: searchData => ({
        url: '/api/v1/flight/fare-calendar?source=LHR&destination=JFK',
        method: 'POST',
        body: searchData,
      }),
      invalidatesTags: ['CalendarDatesList'],
    }),
  }),
});

// ✅ Export hooks for usage in functional components
export const { useGetDatesListMutation } = calendarApi;

'use client';

import { HeroUIProvider } from '@heroui/react';
import { ThemeProvider as NextThemesProvider } from 'next-themes';
import { SessionProvider } from 'next-auth/react';
import { AuthProvider } from './AuthProvider';

import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from '@/store/authStore';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <HeroUIProvider>
        <NextThemesProvider attribute="class" defaultTheme="light">
          <Provider store={store}>
            <PersistGate loading={null} persistor={persistor}>
              <AuthProvider>{children}</AuthProvider>
            </PersistGate>
          </Provider>
        </NextThemesProvider>
      </HeroUIProvider>
    </SessionProvider>
  );
}

// 'use client';

// // app/providers.tsx

// import { HeroUIProvider } from '@heroui/react';
// import { ThemeProvider as NextThemesProvider } from 'next-themes';
// import { SessionProvider } from 'next-auth/react';
// import { AuthProvider } from './AuthProvider';

// export function Providers({ children }: { children: React.ReactNode }) {
//   return (
//     <SessionProvider>
//       <HeroUIProvider>
//         <NextThemesProvider attribute="class" defaultTheme="light">
//           <AuthProvider>{children}</AuthProvider>
//         </NextThemesProvider>
//       </HeroUIProvider>
//     </SessionProvider>
//   );
// }

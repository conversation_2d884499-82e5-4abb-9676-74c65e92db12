import { Chip, Card, CardBody } from '@heroui/react';
import Image from 'next/image';
import Link from 'next/link';

/**
 * Recommendation data structure
 *
 * Integration Notes for API Team:
 * - Ensure API response matches this exact interface
 * - All fields are required for proper display
 * - Image should be a valid URL or base64 string
 * - Tags can be comma-separated string or consider array format
 */
interface Recommendation {
  title: string; // Main recommendation title
  duration: string; // Trip duration (e.g., "4 nights / 5 days")
  location: string; // Destination location
  tags: string; // Comma-separated activity tags
  image_url: string; // Image URL for the destination
  type: string[]; // Status badge (e.g., "Popular", "New")
}

interface RecommendationCardProps {
  recommendations: Recommendation[];
  showViewAll?: boolean;
}

/**
 * RecommendationCard Component
 *
 * Displays a list of travel recommendations with hover effects and responsive design.
 * Each card shows destination image, details, and action buttons.
 *
 * Features:
 * - Hover animations with shadow and transform effects
 * - Responsive image handling with Next.js Image component
 * - Badge overlay on images
 * - Optional "View All" button
 *
 * Integration Notes:
 * - Card height is automatically calculated by parent component
 * - Uses data-card="recommendation" attribute for DOM measurements
 * - Hover effects use Tailwind CSS transitions
 * - Consider adding click handlers for navigation
 */
const RecommendationCard = ({
  recommendations,
  showViewAll = true,
}: RecommendationCardProps | any) => {
  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* Cards container with flexible height */}
      <div className="flex flex-col gap-2 flex-1 overflow-hidden">
        {recommendations.map((item: Recommendation) => (
          <Card
            key={`${item.title}-${item.location}-${item.duration}`}
            className="bg-white border-none shadow-none hover:shadow-lg hover:border-2 hover:border-black hover:-translate-y-1 hover:!bg-white transition-all duration-300 ease-in-out flex-shrink-0"
            isHoverable
            isPressable
            data-card="recommendation" // Used for DOM measurements
          >
            <CardBody>
              <div className="flex items-center justify-between rounded-xl cursor-pointer w-full">
                {/* Left section: Image and details */}
                <div className="flex gap-4">
                  {/* Image with badge overlay */}
                  <div className="relative">
                    <Image
                      src={item.image_url}
                      alt={item.title}
                      width={120}
                      height={95}
                      className="w-[120px] h-[95px] object-cover rounded-xl min-w-[120px] max-w-[120px]"
                    />
                    {/* Status badge positioned over image */}
                    <Chip
                      className="absolute -bottom-2 right-2 font-medium bg-[#C9D7E9] text-[#384254] border-2 border-white text-[10px] rounded-full h-[16px]"
                      color="primary"
                      variant="flat"
                      size="sm"
                    >
                      {item.type[0]}
                    </Chip>
                  </div>
                  {/* Text content */}
                  <div className="text-sm space-y-0.5">
                    <p className="font-medium text-base">{item.title}</p>
                    <p className="text-default-700 text-xs">{item.duration}</p>
                    <p className="text-default-700 text-xs">{item.location}</p>
                    <p className="text-xs text-default-700">{item.tags}</p>
                  </div>
                </div>
                {/* Right section: Action button */}
                <div>
                  <Link
                    // href={`/explore/${item.location
                    //   .toLowerCase()
                    //   .replace(/\s/g, '-')}/${item.title
                    //   .toLowerCase()
                    //   .replace(/\s/g, '-')}`}
                    href={`/explore/${item.location}/${item.title}`}
                    // className="px-5 py-2 rounded-full bg-primary text-white text-sm font-semibold hover:opacity-90 transition text-center"
                  >
                    <Chip
                      color="primary"
                      variant="bordered"
                      size="md"
                      className="font-semibold h-[25px] text-primary border-primary"
                    >
                      Details
                    </Chip>
                  </Link>
                </div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>

      {/* Optional View All button */}
      {showViewAll && (
        <div className="flex justify-end mt-1 flex-shrink-0">
          <Link
            href="/explore/recommendation"
            // className="px-5 py-2 rounded-full bg-primary text-white text-sm font-semibold hover:opacity-90 transition text-center"
          >
            <Chip
              variant="flat"
              size="sm"
              color="primary"
              className="rounded-full font-semibold px-1 h-[24px]"
            >
              View All
            </Chip>
          </Link>
        </div>
      )}
    </div>
  );
};

export default RecommendationCard;

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import Cookies from 'js-cookie';
// Types
export interface LocationSuggestionRequest {
  query: string;
  limit?: number;
}

export interface Location {
  destination_id: number;
  name: string;
  type: string;
}

export const locationApi = createApi({
  reducerPath: 'locationApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT,
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as any).auth?.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      } else {
        const guestId = localStorage.getItem('guest_id');
        if (guestId) {
          headers.set('x-guest-id', guestId);
        }
      }
      return headers;
    },
  }),
  tagTypes: ['LocationSuggestion'],
  endpoints: builder => ({
    // Location suggestions
    getLocationSuggestions: builder.mutation<
      { detail: { data: Location[] } },
      LocationSuggestionRequest
    >({
      query: params => ({
        url: '/api/v1/activities/suggestion',
        method: 'GET',
        params,
      }),
      invalidatesTags: ['LocationSuggestion'],
    }),
  }),
});

export const { useGetLocationSuggestionsMutation } = locationApi;

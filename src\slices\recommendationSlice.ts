// src/slices/recommendationSlice.ts
import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';
import type { Recommendation } from '@/services/recommendationApi';
import type { RootState } from '@/store/authStore';

interface RecommendationState {
  all: Recommendation[];
  filtered: Recommendation[];
  filterTypes: string[];
  selectedTypes: string[];
  cities: string[];
  countries: string[];
  selectedCity: string | null;
}

const initialState: RecommendationState = {
  all: [],
  filtered: [],
  filterTypes: [],
  selectedTypes: [],
  cities: [],
  countries: [],
  selectedCity: null,
};

const recommendationSlice = createSlice({
  name: 'recommendations',
  initialState,
  reducers: {
    setRecommendations: (state, action: PayloadAction<Recommendation[]>) => {
      const all = action.payload;
      const filterTypes = Array.from(
        new Set(all.flatMap(r => (Array.isArray(r.type) ? r.type : [r.type])))
      );
      const cities = Array.from(new Set(all.map(r => r.city)));
      const countries = Array.from(new Set(all.map(r => r.country)));

      return {
        ...state,
        all,
        filtered: all,
        filterTypes,
        cities,
        countries,
      };
    },

    toggleType: (state, action: PayloadAction<string>) => {
      const selectedTypes = state.selectedTypes.includes(action.payload)
        ? state.selectedTypes.filter(t => t !== action.payload)
        : [...state.selectedTypes, action.payload];

      return { ...state, selectedTypes };
    },

    setCity: (state, action: PayloadAction<string | null>) => {
      return { ...state, selectedCity: action.payload };
    },

    applyFilters: state => {
      const filtered = state.all.filter(item => {
        const cityMatch = state.selectedCity
          ? item.city === state.selectedCity
          : true;
        const types = Array.isArray(item.type) ? item.type : [item.type];
        const typeMatch = state.selectedTypes.length
          ? state.selectedTypes.some(t => types.includes(t))
          : true;
        return cityMatch && typeMatch;
      });
      return { ...state, filtered };
    },

    resetFilters: state => {
      return {
        ...state,
        filtered: state.all,
        selectedTypes: [],
        selectedCity: null,
      };
    },
  },
});

export const {
  setRecommendations,
  toggleType,
  setCity,
  applyFilters,
  resetFilters,
} = recommendationSlice.actions;

export default recommendationSlice.reducer;

// Selectors
export const selectAllRecommendations = (state: RootState) =>
  state.recommendations.all;
export const selectFilteredRecommendations = (state: RootState) =>
  state.recommendations.filtered;
export const selectFilterTypes = (state: RootState) =>
  state.recommendations.filterTypes;
export const selectSelectedTypes = (state: RootState) =>
  state.recommendations.selectedTypes;
export const selectCities = (state: RootState) => state.recommendations.cities;
export const selectCountries = (state: RootState) =>
  state.recommendations.countries;
export const selectSelectedCity = (state: RootState) =>
  state.recommendations.selectedCity;
export const selectHasActiveFilters = (state: RootState) =>
  state.recommendations.selectedTypes.length > 0 ||
  state.recommendations.selectedCity !== null;

'use client';

import { useEffect, useCallback, useMemo } from 'react';
import { DateRange, type Range } from 'react-date-range';
import {
  format,
  addDays,
  isWithinInterval,
  startOfDay,
  isSameDay,
} from 'date-fns';
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';
import './custom-calendar.css';
import { Radio, RadioGroup } from '@heroui/react';
import { useDispatch, useSelector } from 'react-redux';
import {
  setDateRange,
  setRanges,
  selectDateRange,
} from '@/slices/calendarSlice';

import { useGetDatesListMutation } from '@/services/calendarApi';

const borderColors: Record<string, string> = {
  low: '2px solid green',
  medium: '2px solid orange',
  high: '2px solid red',
};

export default function CustomCalendar() {
  const dispatch = useDispatch();
  const { startDate, endDate, ranges } = useSelector(selectDateRange);

  const [getDatesList] = useGetDatesListMutation();

  // ✅ Default selection → today to today+4 (5 days)
  useEffect(() => {
    if (!startDate && !endDate) {
      const today = startOfDay(new Date());
      const defaultEnd = startOfDay(addDays(today, 4));
      dispatch(
        setDateRange({
          startDate: today.toISOString(),
          endDate: defaultEnd.toISOString(),
        })
      );
    }
  }, [startDate, endDate, dispatch]);

  useEffect(() => {
    if (startDate && endDate) {
      getDatesList({ startDate, endDate })
        .unwrap()
        .then((res: any) => {
          const map: Record<string, string> = {};
          res?.detail?.data?.forEach((item: any) => {
            map[String(item.date)] = String(item.range);
          });
          dispatch(setRanges(map));
        })
        .catch(() => {
          // ignore errors
        });
    }
  }, [startDate, endDate, getDatesList, dispatch]);

  const start = useMemo(
    () => (startDate ? new Date(startDate) : undefined),
    [startDate]
  );
  const end = useMemo(
    () => (endDate ? new Date(endDate) : undefined),
    [endDate]
  );

  const range: Range[] = [
    {
      startDate: start || new Date(),
      endDate: end || start || new Date(),
      key: 'selection',
    },
  ];

  const renderDayContent = useCallback(
    (day: Date) => {
      const iso = format(day, 'yyyy-MM-dd');
      const rangeType = ranges[iso];

      const today = startOfDay(new Date());
      const fiveDaysLater = startOfDay(addDays(today, 4));
      const isHighlight = isWithinInterval(startOfDay(day), {
        start: today,
        end: fiveDaysLater,
      });

      return (
        <div className="w-full h-full flex items-center justify-center">
          <span
            style={{
              display: 'inline-block',
              width: '28px',
              height: '28px',
              lineHeight: '28px',
              borderBottom:
                isHighlight && rangeType ? borderColors[rangeType] : '',
              textAlign: 'center',
              backgroundColor: isHighlight ? '#E9E7FB' : 'transparent',
              borderRadius: '50%',
            }}
          >
            {format(day, 'd')}
          </span>
        </div>
      );
    },
    [ranges]
  );

  const handleDatechange = (item: any) => {
    let nextStart = item?.selection?.startDate
      ? startOfDay(new Date(item.selection.startDate))
      : null;
    let nextEnd = item?.selection?.endDate
      ? startOfDay(new Date(item.selection.endDate))
      : null;

    if (!nextStart) return;

    // ✅ Enforce proper range logic
    // If user picks an "end" earlier than "start",
    // treat it as restarting selection (new start, no end yet).
    if (nextEnd && nextEnd < nextStart) {
      nextStart = nextEnd;
      nextEnd = null;
    }

    // If user just clicked once → react-date-range sets start=end
    // We clear end until they explicitly pick a later date.
    if (nextEnd && isSameDay(nextStart, nextEnd)) {
      nextEnd = null;
    }

    dispatch(
      setDateRange({
        startDate: nextStart.toISOString(),
        endDate: nextEnd ? nextEnd.toISOString() : '',
      })
    );
  };

  // ✅ Header text
  const headerText = useMemo(() => {
    if (!start) return 'Select a date range';
    const startTxt = format(start, 'dd-MM-yyyy');
    if (end && !isSameDay(start, end)) {
      const endTxt = format(end, 'dd-MM-yyyy');
      return `${startTxt} - ${endTxt}`;
    }
    return `${startTxt} - Select end date`;
  }, [start, end]);

  return (
    <div className="calendar-wrapper">
      <div className="calendar-header">
        <RadioGroup orientation="horizontal">
          <Radio value="calendar">Calendar</Radio>
          <Radio value="flex">Flexible Dates</Radio>
        </RadioGroup>

        <div className="mb-4 text-lg font-semibold text-gray-700">
          {headerText}
        </div>
      </div>

      <DateRange
        editableDateInputs
        onChange={handleDatechange}
        moveRangeOnFirstSelection={false}
        months={2}
        direction="horizontal"
        rangeColors={['#E9E7FB']}
        ranges={range}
        showDateDisplay={false}
        minDate={startOfDay(new Date())}
        dayContentRenderer={renderDayContent}
      />
    </div>
  );
}

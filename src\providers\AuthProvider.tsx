'use client';

import type { ReactNode } from 'react';
import { createContext, useEffect, useState } from 'react';
import {
  useRefreshTokenMutation,
  useGuestLoginMutation,
} from '@/services/authApi';

interface User {
  id?: string;
  email?: string;
  firstname?: string;
  lastname?: string;
  guest_id?: string;
}

interface AuthContextType {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  setTokens: (
    access: string,
    refresh: string,
    user?: User,
    expiresIn?: number
  ) => void;
  logout: () => void;
}

export const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [refreshToken, setRefreshToken] = useState<string | null>(null);

  const [triggerRefresh] = useRefreshTokenMutation();
  const [triggerGuestLogin] = useGuestLoginMutation();

  // ✅ Guest login fallback
  const doGuestLogin = async () => {
    try {
      const res = await triggerGuestLogin().unwrap();
      if (res?.detail?.data?.guest_id) {
        const guestUser = { guest_id: res.detail.data.guest_id };
        setUser(guestUser);
        setAccessToken(null);
        setRefreshToken(null);
        localStorage.setItem('user', JSON.stringify(guestUser));
        localStorage.setItem('guest_id', res.detail.data.guest_id);
      }
    } catch (err) {
      console.error('Guest login failed:', err);
    }
  };

  // ✅ Logout
  const logout = () => {
    setUser(null);
    setAccessToken(null);
    setRefreshToken(null);
    localStorage.removeItem('token'); // FIX: match slice
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    localStorage.removeItem('guest_id');
    doGuestLogin(); // fallback to guest after logout
  };

  useEffect(() => {
    const savedAccess = localStorage.getItem('token');
    const savedRefresh = localStorage.getItem('refreshToken');
    const savedUserRaw = localStorage.getItem('user');
    const savedExpiresAt = localStorage.getItem('tokenExpiresAt');

    let savedUser: User | null = null;
    if (
      savedUserRaw &&
      savedUserRaw !== 'undefined' &&
      savedUserRaw !== 'null'
    ) {
      try {
        savedUser = JSON.parse(savedUserRaw);
      } catch (e) {
        console.warn('Invalid saved user JSON, clearing storage');
        localStorage.removeItem('user');
      }
    }

    if (savedAccess && savedRefresh) {
      setAccessToken(savedAccess);
      setRefreshToken(savedRefresh);
      if (savedUser) setUser(savedUser);

      const isExpired =
        savedExpiresAt && Date.now() >= parseInt(savedExpiresAt, 10);

      if (isExpired) {
        // 🔄 Refresh if expired
        triggerRefresh({ refreshToken: savedRefresh })
          .unwrap()
          .then(res => {
            const newAccess = res?.detail?.data?.accessToken;
            const newUser = res?.detail?.data?.user;
            const expiresIn = res?.detail?.data?.accessTokenExpireOn;

            if (newAccess) {
              setAccessToken(newAccess);
              localStorage.setItem('token', newAccess);

              if (expiresIn) {
                const newExpiresAt = Date.now() + expiresIn * 1000;
                localStorage.setItem('tokenExpiresAt', newExpiresAt.toString());
              }
            }

            if (newUser) {
              setUser(newUser);
              localStorage.setItem('user', JSON.stringify(newUser));
            }
          })
          .catch(() => {
            logout();
            doGuestLogin();
          });
      }
    } else {
      doGuestLogin();
    }
  }, [triggerRefresh]);

  // ✅ Save real login tokens
  const setTokens = (
    access: string,
    refresh: string,
    userData?: User,
    expiresIn?: number
  ) => {
    setAccessToken(access);
    setRefreshToken(refresh);
    if (userData) {
      setUser(userData);
      localStorage.setItem('user', JSON.stringify(userData));
    }
    localStorage.setItem('token', access); // FIX: use "token" to match slice
    localStorage.setItem('refreshToken', refresh);
    if (expiresIn) {
      const expiresAt = Date.now() + expiresIn * 1000;
      localStorage.setItem('tokenExpiresAt', expiresAt.toString());
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        accessToken,
        refreshToken,
        isAuthenticated: !!accessToken,
        setTokens,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

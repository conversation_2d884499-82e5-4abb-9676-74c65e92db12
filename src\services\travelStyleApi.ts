// src/services/travelStyleApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

interface TravelStyle {
  style: string;
  tags: string[];
}

interface TravelStyleResponse {
  message: string;
  detail: {
    data: TravelStyle[];
  };
}

export const travelStyleApi = createApi({
  reducerPath: 'travelStyleApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT,
    prepareHeaders: (headers, { getState }) => {
      const state = getState() as any;
      const token = state.auth?.token;
      // const guestId = state.auth?.guestId; // fallback if no token

      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      } else {
        const guestId = localStorage.getItem('guest_id');
        if (guestId) {
          headers.set('x-guest-id', guestId);
        }
      }
      return headers;
    },
  }),
  tagTypes: ['TravelStyle'],
  endpoints: builder => ({
    getTravelStyleSuggestions: builder.query<TravelStyleResponse, void>({
      query: () => '/api/v1/activities/suggestions/travel-style',
      providesTags: ['TravelStyle'],
    }),
  }),
});

export const { useGetTravelStyleSuggestionsQuery } = travelStyleApi;
